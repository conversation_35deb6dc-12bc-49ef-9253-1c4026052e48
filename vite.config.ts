import path from "path";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    // Minimal plugins for production build
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Optimize for production deployment
    minify: 'esbuild', // Use esbuild instead of terser for faster builds
    sourcemap: false,
    rollupOptions: {
      output: {
        // Ensure consistent file naming for caching
        assetFileNames: 'assets/[name]-[hash][extname]',
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
      },
    },
  },
  // Ensure proper base path for S3 deployment
  base: mode === 'production' ? '/' : '/',
}));
