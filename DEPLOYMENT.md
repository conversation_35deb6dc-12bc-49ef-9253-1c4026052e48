# RatioHub AWS S3 Deployment Guide

## Overview
This guide explains how to deploy the RatioHub React application to AWS S3 with CloudFront CDN for global distribution.

## Prerequisites

### 1. AWS Account Setup
- AWS account with appropriate permissions
- AWS CLI installed on your machine
- Access to create S3 buckets and CloudFront distributions

### 2. Local Development Environment
- Node.js (v18 or higher)
- npm or yarn package manager
- Git for version control

## Deployment Configuration

### AWS Resources
- **S3 Bucket**: `ratiohub` (ap-south-1 region)
- **CloudFront Distribution**: Global CDN for fast content delivery
- **IAM Permissions**: S3 and CloudFront access

### Build Configuration
- **Build Tool**: Vite
- **Output Directory**: `dist/`
- **Asset Optimization**: Minified with hash-based file names
- **Cache Strategy**: Long-term caching for assets, no-cache for HTML

## Step-by-Step Deployment

### Step 1: Initial AWS Setup
```bash
# Make scripts executable (first time only)
chmod +x setup-aws.sh deploy-to-s3.sh

# Run AWS setup
./setup-aws.sh
# or
npm run setup-aws
```

This script will:
- Verify AWS CLI installation
- Configure AWS credentials
- Test credential access
- Create S3 bucket if it doesn't exist
- Set up basic bucket configuration

### Step 2: Deploy Application
```bash
# Deploy to S3
./deploy-to-s3.sh
# or
npm run deploy
```

This script will:
- Install dependencies (if needed)
- Build the React application with Vite
- Upload files to S3 with optimized caching headers
- Configure S3 for static website hosting
- Set public read permissions
- Invalidate CloudFront cache (when configured)

### Step 3: CloudFront Setup (Manual)
After initial deployment:
1. Create CloudFront distribution in AWS Console
2. Set S3 bucket as origin
3. Configure caching behaviors
4. Update deployment script with distribution ID

## File Structure After Deployment

```
S3 Bucket (ratiohub)
├── index.html                 # Main entry point (no-cache)
├── assets/                    # Static assets (long-term cache)
│   ├── index-[hash].js        # Main JavaScript bundle
│   ├── index-[hash].css       # Main CSS bundle
│   └── [name]-[hash].[ext]    # Other assets
└── images/                    # Image assets (medium-term cache)
    └── [image files]
```

## Caching Strategy

### Long-term Cache (1 year)
- **Files**: `/assets/*` - JS, CSS, and other hashed assets
- **Headers**: `Cache-Control: public, max-age=31536000, immutable`
- **Reason**: Files have content-based hashes, safe for long caching

### Medium-term Cache (1 day)
- **Files**: `/images/*` - Image assets
- **Headers**: `Cache-Control: public, max-age=86400`
- **Reason**: Images change less frequently but may be updated

### Short-term Cache (1 hour)
- **Files**: Other files except index.html
- **Headers**: `Cache-Control: public, max-age=3600`
- **Reason**: General files that may change occasionally

### No Cache
- **Files**: `index.html`
- **Headers**: `Cache-Control: no-cache, no-store, must-revalidate`
- **Reason**: Entry point must always fetch latest version

## Environment Variables

### Production Environment
Create `.env.production` file:
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://rgcxrksmdkzarcdlscpk.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key

# Application Configuration
VITE_APP_ENV=production
VITE_APP_URL=https://your-cloudfront-domain.cloudfront.net
```

### Development Environment
Create `.env.development` file:
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://rgcxrksmdkzarcdlscpk.supabase.co
VITE_SUPABASE_ANON_KEY=your_development_anon_key

# Application Configuration
VITE_APP_ENV=development
VITE_APP_URL=http://localhost:8080
```

## Deployment Commands

```bash
# Initial setup (run once)
npm run setup-aws

# Deploy to production
npm run deploy

# Build only (without deployment)
npm run build

# Preview build locally
npm run preview
```

## Troubleshooting

### Common Issues

#### 1. AWS Credentials Not Found
```bash
Error: AWS credentials are not configured
```
**Solution**: Run `./setup-aws.sh` or configure AWS CLI manually

#### 2. S3 Bucket Access Denied
```bash
Error: Access Denied when accessing S3 bucket
```
**Solution**: Check IAM permissions for S3 access

#### 3. Build Fails
```bash
Error: Build failed - 'dist' directory not found
```
**Solution**: Check Vite configuration and ensure build script works locally

#### 4. CloudFront Not Invalidating
```bash
Warning: CloudFront distribution ID not set
```
**Solution**: Update `CLOUDFRONT_DISTRIBUTION_ID` in `deploy-to-s3.sh`

### Verification Steps

1. **Check S3 Upload**:
   - Visit S3 console: https://s3.console.aws.amazon.com/s3/buckets/ratiohub
   - Verify files are uploaded with correct timestamps

2. **Test Website**:
   - S3 Website URL: http://ratiohub.s3-website.ap-south-1.amazonaws.com
   - CloudFront URL: https://your-distribution.cloudfront.net

3. **Verify Caching**:
   - Check browser developer tools for cache headers
   - Ensure assets have long cache times

## Security Considerations

### S3 Bucket Policy
- Public read access for website files
- No write access from public
- Bucket policy restricts to GetObject only

### CloudFront Security
- HTTPS enforcement
- Geographic restrictions (if needed)
- WAF integration (optional)

### Environment Variables
- Never commit sensitive keys to git
- Use different keys for development/production
- Rotate keys regularly

## Performance Optimization

### Build Optimization
- Minified JavaScript and CSS
- Tree shaking for unused code
- Code splitting for better caching
- Optimized asset loading

### CDN Benefits
- Global edge locations
- Reduced latency
- Bandwidth optimization
- DDoS protection

## Monitoring and Maintenance

### CloudWatch Metrics
- S3 request metrics
- CloudFront performance metrics
- Error rate monitoring

### Regular Tasks
- Monitor AWS costs
- Update dependencies
- Rotate access keys
- Review security settings

## Cost Optimization

### S3 Costs
- Use appropriate storage class
- Enable lifecycle policies for old versions
- Monitor request patterns

### CloudFront Costs
- Choose appropriate price class
- Optimize cache hit ratio
- Monitor data transfer costs

---

For questions or issues, refer to the AWS documentation or contact the development team.
