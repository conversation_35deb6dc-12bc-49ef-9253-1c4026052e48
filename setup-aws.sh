#!/bin/bash

# AWS Setup Script for RatioHub
# This script helps you configure AWS credentials securely

echo "🔧 AWS Setup for RatioHub Deployment"
echo "===================================="
echo ""

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed."
    echo ""
    echo "Please install AWS CLI first:"
    echo "  macOS: brew install awscli"
    echo "  Linux: sudo apt-get install awscli"
    echo "  Windows: Download from https://aws.amazon.com/cli/"
    echo ""
    exit 1
fi

echo "✅ AWS CLI is installed"
echo ""

# Configure AWS credentials
echo "🔑 Configuring AWS credentials..."
echo ""
echo "Please enter your AWS credentials:"
echo "(These will be stored securely in ~/.aws/credentials)"
echo ""

# Use aws configure to set up credentials securely
# Update these with your current credentials
aws configure set aws_access_key_id "********************"
aws configure set aws_secret_access_key "TGGvrD0/FdPXcqGMqJi9DdwSM/HOZNAkQJVOplaW"
aws configure set default.region "ap-south-1"
aws configure set default.output "json"

echo ""
echo "✅ AWS credentials configured"
echo ""

# Test credentials
echo "🧪 Testing AWS credentials..."
if aws sts get-caller-identity &> /dev/null; then
    echo "✅ AWS credentials are working"
    echo ""
    aws sts get-caller-identity
else
    echo "❌ AWS credentials test failed"
    echo "Please check your credentials and try again"
    exit 1
fi

echo ""
echo "🎉 AWS setup complete!"
echo ""
echo "Next steps:"
echo "1. Make sure your S3 bucket 'ratiohub' exists"
echo "2. Run: npm run deploy"
echo ""

# Security reminder
echo "🔒 SECURITY REMINDER:"
echo "- Your credentials are now stored in ~/.aws/credentials"
echo "- Never commit AWS credentials to git"
echo "- Consider rotating these credentials after setup"
echo "- Use IAM roles in production environments"
echo ""

# Create S3 bucket if it doesn't exist
echo "🪣 Checking if S3 bucket 'ratiohub' exists..."
if aws s3 ls "s3://ratiohub" &> /dev/null; then
    echo "✅ S3 bucket 'ratiohub' already exists"
else
    echo "📦 Creating S3 bucket 'ratiohub'..."
    aws s3 mb s3://ratiohub --region ap-south-1
    if [ $? -eq 0 ]; then
        echo "✅ S3 bucket 'ratiohub' created successfully"
    else
        echo "❌ Failed to create S3 bucket 'ratiohub'"
        echo "Please create it manually or check your permissions"
    fi
fi

echo ""
echo "🚀 Ready for deployment!"
echo "Run: ./deploy-to-s3.sh"
