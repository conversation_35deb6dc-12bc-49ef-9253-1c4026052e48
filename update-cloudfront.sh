#!/bin/bash

# CloudFront Configuration Update Script for RatioHub
# Run this script after creating your CloudFront distribution

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🚀 CloudFront Configuration Update for RatioHub"
echo "==============================================="
echo ""

# Check if deploy script exists
if [ ! -f "deploy-to-s3.sh" ]; then
    print_error "deploy-to-s3.sh not found in current directory"
    exit 1
fi

# Get CloudFront distribution ID from user
echo "Please enter your CloudFront distribution ID:"
echo "(You can find this in the AWS CloudFront console)"
read -p "Distribution ID: " DISTRIBUTION_ID

if [ -z "$DISTRIBUTION_ID" ]; then
    print_error "Distribution ID cannot be empty"
    exit 1
fi

# Get CloudFront domain from user
echo ""
echo "Please enter your CloudFront domain name:"
echo "(e.g., d1234567890123.cloudfront.net)"
read -p "CloudFront Domain: " CLOUDFRONT_DOMAIN

if [ -z "$CLOUDFRONT_DOMAIN" ]; then
    print_error "CloudFront domain cannot be empty"
    exit 1
fi

# Update the deployment script
print_status "Updating deploy-to-s3.sh with CloudFront configuration..."

# Create backup
cp deploy-to-s3.sh deploy-to-s3.sh.backup
print_status "Created backup: deploy-to-s3.sh.backup"

# Update distribution ID
sed -i.tmp "s/CLOUDFRONT_DISTRIBUTION_ID=\"DUMMY_DISTRIBUTION_ID\"/CLOUDFRONT_DISTRIBUTION_ID=\"$DISTRIBUTION_ID\"/" deploy-to-s3.sh

# Update domain
sed -i.tmp "s/CLOUDFRONT_DOMAIN=\"dummy-cloudfront-domain.cloudfront.net\"/CLOUDFRONT_DOMAIN=\"$CLOUDFRONT_DOMAIN\"/" deploy-to-s3.sh

# Clean up temp files
rm -f deploy-to-s3.sh.tmp

print_success "Updated deploy-to-s3.sh with CloudFront configuration"

# Verify the changes
echo ""
print_status "Verifying configuration..."
echo "Distribution ID: $DISTRIBUTION_ID"
echo "CloudFront Domain: $CLOUDFRONT_DOMAIN"

# Test CloudFront distribution
print_status "Testing CloudFront distribution..."
if aws cloudfront get-distribution --id "$DISTRIBUTION_ID" &> /dev/null; then
    print_success "CloudFront distribution is accessible"
else
    print_warning "Could not access CloudFront distribution. Please check:"
    echo "  1. Distribution ID is correct"
    echo "  2. AWS credentials have CloudFront permissions"
    echo "  3. Distribution exists and is deployed"
fi

echo ""
print_success "CloudFront configuration complete!"
echo ""
echo "Next steps:"
echo "1. Run: npm run deploy"
echo "2. Wait for CloudFront cache invalidation (10-15 minutes)"
echo "3. Test your site at: https://$CLOUDFRONT_DOMAIN"
echo ""
echo "Your RatioHub application will be available at:"
echo "🌐 S3 Website: http://ratiohub.s3-website.ap-south-1.amazonaws.com"
echo "🚀 CloudFront: https://$CLOUDFRONT_DOMAIN"
