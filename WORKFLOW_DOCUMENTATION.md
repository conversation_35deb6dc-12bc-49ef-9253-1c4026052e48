# RatioHub Project Management System - Workflow Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Project Management Workflow](#project-management-workflow)
4. [Task Management Workflow](#task-management-workflow)
5. [Team Management Workflow](#team-management-workflow)
6. [Time Tracking & Billing Workflow](#time-tracking--billing-workflow)
7. [Support Ticket Workflow](#support-ticket-workflow)
8. [Data Persistence & Integration](#data-persistence--integration)
9. [Gap Analysis](#gap-analysis)

## System Overview

RatioHub is a comprehensive project management system built with React/TypeScript frontend and Supabase backend. The system implements role-based access control with four user roles: `admin`, `project_manager`, `user`, and `client`.

### Core Architecture
- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **State Management**: React Query (TanStack Query)
- **UI Framework**: Tailwind CSS + shadcn/ui
- **Authentication**: Supabase Auth with JWT tokens

## Authentication & Authorization

### Current Implementation Status: ✅ COMPLETE

#### Workflow Steps:
1. **User Registration/Login**
   - Route: `/auth`
   - Component: `src/pages/Auth.tsx`
   - Functions: `signUp()`, `signIn()`, `signOut()`

2. **Session Management**
   - Context: `src/components/auth/AuthContext.tsx`
   - Hook: `src/hooks/useUserRole.tsx`
   - Auto-refresh tokens and persistent sessions

3. **Role-Based Access Control**
   - Component: `src/components/auth/ProtectedRoute.tsx`
   - Database: `user_roles` table with enum `app_role`
   - RLS Policies: Comprehensive row-level security

#### Database Tables Involved:
- `auth.users` (Supabase managed)
- `profiles` (user profile data)
- `user_roles` (role assignments)

#### API Endpoints:
- Supabase Auth API (built-in)
- Custom functions: `is_admin()`, `is_project_manager()`, `has_role()`

#### Data Validation:
- Email validation on signup
- Password strength requirements
- Role-based route protection
- RLS policies for data access

## Project Management Workflow

### Current Implementation Status: ✅ MOSTLY COMPLETE

#### Workflow Steps:

1. **Project Creation**
   - Route: `/projects`
   - Component: `src/components/projects/ProjectCreateSheet.tsx`
   - Auto-generates project ID using format: `XX###` (e.g., `GM001`)

2. **Project ID Generation**
   - Function: `generate_project_id(project_name)`
   - Trigger: `trigger_set_project_id` (BEFORE INSERT)
   - Logic: Extract 2 chars from name + 3-digit sequence

3. **Project Management**
   - List View: `src/pages/Projects.tsx`
   - Detail View: `src/pages/ProjectDetails.tsx`
   - CRUD Operations: Full implementation

#### Database Tables:
- `projects` (main project data)
- `phases` (project phases)
- `milestones` (project milestones)

#### API Operations:
```sql
-- Create Project
INSERT INTO projects (name, description, status, created_by, team_id, client_id, budget, currency, hourly_rate, is_billable, start_date, end_date)

-- Auto-generated project_id via trigger
-- Status options: 'planning', 'active', 'on_hold', 'completed', 'cancelled'
```

#### Data Validation:
- Required fields: name, created_by
- Auto-generated: project_id, created_at, updated_at
- Foreign keys: team_id → teams, client_id → profiles
- RLS: Project stakeholders can manage projects

## Task Management Workflow

### Current Implementation Status: ✅ COMPLETE

#### Workflow Steps:

1. **Task Creation**
   - Component: `src/components/projects/TaskCreateSheet.tsx`
   - Auto-generates hierarchical task ID: `PROJECT_ID-T###` (e.g., `GM001-T001`)

2. **Task ID Generation**
   - Function: `generate_task_id(proj_id)`
   - Trigger: `trigger_set_task_id` (BEFORE INSERT)
   - Format: `{project_code}-T{sequence}`

3. **Task Status Management**
   - Component: `src/components/tasks/TaskStatusManager.tsx`
   - Status transitions: `todo` → `in_progress` → `review` → `completed` → `blocked`

4. **Task Assignment & Notifications**
   - Auto-notification on assignment
   - Function: `auto_create_notifications()`
   - Trigger: AFTER UPDATE on tasks

#### Database Tables:
- `tasks` (main task data)
- `task_comments` (task discussions)
- `notifications` (auto-generated)

#### API Operations:
```sql
-- Create Task
INSERT INTO tasks (title, description, priority, due_date, estimated_hours, milestone_id, assigned_to, project_id, created_by, status, tags)

-- Update Task Status
UPDATE tasks SET status = $1 WHERE id = $2

-- Task Status Options: 'todo', 'in_progress', 'review', 'completed', 'blocked'
-- Priority Options: 'low', 'medium', 'high', 'critical'
```

#### Data Validation:
- Required: title, project_id, created_by
- Auto-generated: task_id, created_at, updated_at
- Foreign keys: project_id → projects, milestone_id → milestones, assigned_to → profiles
- Business logic: Auto-calculate actual_hours when completed

## Team Management Workflow

### Current Implementation Status: ✅ COMPLETE

#### Workflow Steps:

1. **Team Creation**
   - Route: `/teams` (Admin/PM only)
   - Component: `src/pages/Teams.tsx`
   - Access Control: `allowedRoles={['admin', 'project_manager']}`

2. **Team Member Assignment**
   - Component: `src/pages/TeamDetails.tsx`
   - Roles: `lead`, `member`
   - Junction table: `team_members`

3. **User Invitation System**
   - Function: `supabase/functions/send-invitation/index.ts`
   - Email integration: Resend API
   - Token-based invitations

#### Database Tables:
- `teams` (team information)
- `team_members` (user-team relationships)
- `user_invitations` (pending invitations)

#### API Operations:
```sql
-- Create Team
INSERT INTO teams (name, description, created_by, is_active)

-- Add Team Member
INSERT INTO team_members (team_id, user_id, role, joined_at)

-- Send Invitation
INSERT INTO user_invitations (email, role, team_ids, invited_by, metadata)
```

#### Data Validation:
- Required: team name, created_by
- Foreign keys: created_by → profiles, team_id → teams, user_id → profiles
- RLS: Team members and managers can view/edit teams

## Time Tracking & Billing Workflow

### Current Implementation Status: ✅ COMPLETE

#### Workflow Steps:

1. **Time Tracking**
   - Route: `/time-tracking`
   - Components: 
     - `src/components/time/TimeTracker.tsx` (live tracking)
     - `src/components/time/ManualTimeEntry.tsx` (manual entry)

2. **Billing Calculation**
   - Auto-calculation: `duration_minutes * hourly_rate / 60`
   - Project-level and task-level rates
   - Billable/non-billable flag

3. **Time Entry Management**
   - CRUD operations on time entries
   - Project and task association
   - Real-time duration calculation

#### Database Tables:
- `time_entries` (time tracking data)
- `projects` (hourly rates)
- `tasks` (task-specific rates)

#### API Operations:
```sql
-- Create Time Entry
INSERT INTO time_entries (user_id, project_id, task_id, description, start_time, end_time, duration_minutes, is_billable, hourly_rate)

-- Calculate Duration
UPDATE time_entries SET duration_minutes = EXTRACT(EPOCH FROM (end_time - start_time))/60

-- Billing Query
SELECT SUM(duration_minutes * hourly_rate / 60) as total_amount FROM time_entries WHERE is_billable = true
```

#### Data Validation:
- Required: user_id, project_id, start_time
- Auto-calculated: duration_minutes (if end_time provided)
- Foreign keys: user_id → profiles, project_id → projects, task_id → tasks
- Business logic: Inherit hourly_rate from project if not specified

## Support Ticket Workflow

### Current Implementation Status: ✅ COMPLETE

#### Workflow Steps:

1. **Ticket Creation**
   - Route: `/support-tickets`
   - Auto-generates ticket ID: `TKT-###` format

2. **Ticket ID Generation**
   - Function: `generate_ticket_id()`
   - Trigger: `trigger_set_ticket_id` (BEFORE INSERT)
   - Sequential numbering: TKT-001, TKT-002, etc.

3. **Ticket Lifecycle**
   - Status: `open` → `in_progress` → `resolved` → `closed`
   - Priority: `low`, `medium`, `high`, `urgent`
   - Assignment notifications

#### Database Tables:
- `support_tickets` (ticket data)
- `notifications` (assignment alerts)

#### API Operations:
```sql
-- Create Ticket
INSERT INTO support_tickets (title, description, status, priority, created_by, assigned_to, project_id)

-- Status Options: 'open', 'in_progress', 'resolved', 'closed'
-- Priority Options: 'low', 'medium', 'high', 'urgent'
```

#### Data Validation:
- Required: title, description, created_by
- Auto-generated: ticket_id, created_at, updated_at
- Foreign keys: created_by → profiles, assigned_to → profiles, project_id → projects
- Notifications: Auto-notify on assignment

## Data Persistence & Integration

### Database Schema Relationships

```mermaid
graph TD
    A[auth.users] --> B[profiles]
    B --> C[user_roles]
    B --> D[teams]
    D --> E[team_members]
    B --> F[projects]
    F --> G[phases]
    G --> H[milestones]
    F --> I[tasks]
    H --> I
    I --> J[task_comments]
    F --> K[time_entries]
    I --> K
    F --> L[support_tickets]
    B --> L
    F --> M[file_attachments]
    I --> M
```

### Auto-Generated Fields

1. **Project IDs**: `XX###` format (e.g., GM001, TS002)
2. **Task IDs**: `PROJECT_ID-T###` format (e.g., GM001-T001)
3. **Ticket IDs**: `TKT-###` format (e.g., TKT-001)
4. **Timestamps**: `created_at`, `updated_at` (auto-managed)

### Trigger Functions

1. **ID Generation Triggers**:
   - `trigger_set_project_id` → `set_project_id()`
   - `trigger_set_task_id` → `set_task_id()`
   - `trigger_set_ticket_id` → `set_ticket_id()`

2. **Notification Triggers**:
   - `task_notifications` → `auto_create_notifications()`
   - `ticket_notifications` → `auto_create_notifications()`

3. **Business Logic Triggers**:
   - `auto_update_task_time` → Calculate actual hours on completion
   - `update_*_updated_at` → Auto-update timestamps

### Row Level Security (RLS)

All tables implement comprehensive RLS policies:
- **Admin**: Full access to all data
- **Project Manager**: Access to assigned projects and teams
- **User**: Access to assigned tasks and own data
- **Client**: Limited access to own projects and tickets

## Gap Analysis

### ✅ Fully Implemented
- Authentication & Authorization
- Project CRUD operations
- Task management with hierarchical IDs
- Team management and invitations
- Time tracking and billing
- Support ticket system
- Auto-ID generation
- Notification system
- RLS security

### ⚠️ Partially Implemented
- **Reporting & Analytics**: Basic structure exists but needs enhancement
- **File Attachments**: Database schema exists but UI implementation incomplete
- **Client Portal**: Route exists but limited functionality
- **Calendar Integration**: Basic route but needs full implementation
- **Advanced Billing**: Invoice generation partially implemented

### ❌ Missing/Incomplete
- **Real-time Collaboration**: WebSocket integration for live updates
- **Advanced Project Templates**: Predefined project structures
- **Resource Management**: Equipment and resource allocation
- **Advanced Reporting**: Custom report builder
- **API Documentation**: Comprehensive API docs
- **Mobile Responsiveness**: Full mobile optimization
- **Bulk Operations**: Bulk task/project operations
- **Advanced Search**: Full-text search across entities
- **Audit Logging**: Comprehensive activity tracking
- **Data Export**: CSV/PDF export functionality

### Priority Recommendations

1. **High Priority**:
   - Complete file attachment UI
   - Enhance client portal functionality
   - Implement real-time updates
   - Add bulk operations

2. **Medium Priority**:
   - Advanced reporting dashboard
   - Mobile optimization
   - API documentation
   - Data export features

3. **Low Priority**:
   - Project templates
   - Resource management
   - Advanced search
   - Audit logging enhancements

## Technical Implementation Details

### Frontend Architecture

#### Component Structure
```
src/
├── components/
│   ├── auth/                 # Authentication components
│   ├── projects/            # Project management UI
│   ├── tasks/               # Task management UI
│   ├── time/                # Time tracking UI
│   ├── teams/               # Team management UI
│   ├── admin/               # Admin panel components
│   └── layout/              # Layout components
├── pages/                   # Route components
├── hooks/                   # Custom React hooks
├── integrations/supabase/   # Supabase client & types
└── lib/                     # Utility functions
```

#### State Management
- **React Query**: Server state management and caching
- **React Context**: Authentication state
- **Local State**: Component-level state with useState/useReducer

#### Key Hooks
- `useAuth()`: Authentication context
- `useUserRole()`: Role-based access control
- `useQuery()`: Data fetching with caching
- `useMutation()`: Data mutations with optimistic updates

### Backend Architecture

#### Database Functions
```sql
-- ID Generation Functions
generate_project_id(project_name text) → text
generate_task_id(proj_id uuid) → text
generate_ticket_id() → text

-- Role Checking Functions
is_admin(user_id uuid) → boolean
is_project_manager(user_id uuid) → boolean
has_role(user_id uuid, role app_role) → boolean

-- Notification Functions
create_notification(user_id uuid, title text, message text, type text, action_url text) → uuid
auto_create_notifications() → trigger

-- Business Logic Functions
auto_update_task_time() → trigger
log_project_activity() → void
```

#### Enum Types
```sql
app_role: 'admin', 'project_manager', 'user', 'client'
project_status: 'planning', 'active', 'on_hold', 'completed', 'cancelled'
task_status: 'todo', 'in_progress', 'review', 'completed', 'blocked'
task_priority: 'low', 'medium', 'high', 'critical'
ticket_status: 'open', 'in_progress', 'resolved', 'closed'
ticket_priority: 'low', 'medium', 'high', 'urgent'
```

### API Patterns

#### Standard CRUD Operations
```typescript
// Create
const { data, error } = await supabase
  .from('table_name')
  .insert(data)
  .select()
  .single();

// Read with relationships
const { data, error } = await supabase
  .from('tasks')
  .select(`
    *,
    milestone:milestones(name),
    assigned_to_profile:profiles(first_name, last_name)
  `)
  .eq('project_id', projectId);

// Update with optimistic updates
const updateMutation = useMutation({
  mutationFn: async (data) => {
    const { error } = await supabase
      .from('table_name')
      .update(data)
      .eq('id', id);
    if (error) throw error;
  },
  onSuccess: () => {
    queryClient.invalidateQueries(['query-key']);
  }
});
```

#### Real-time Subscriptions
```typescript
// Subscribe to table changes
useEffect(() => {
  const subscription = supabase
    .channel('table-changes')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'table_name' },
      (payload) => {
        // Handle real-time updates
        queryClient.invalidateQueries(['query-key']);
      }
    )
    .subscribe();

  return () => subscription.unsubscribe();
}, []);
```

### Security Implementation

#### Row Level Security Policies
```sql
-- Example: Project access policy
CREATE POLICY "Project stakeholders can manage projects"
ON public.projects FOR ALL
USING (
  is_admin(auth.uid()) OR
  is_project_manager(auth.uid()) OR
  created_by = auth.uid() OR
  EXISTS (
    SELECT 1 FROM team_members tm
    WHERE tm.team_id = projects.team_id
    AND tm.user_id = auth.uid()
  )
);
```

#### Frontend Route Protection
```typescript
// Role-based route protection
<Route
  path="/admin"
  element={
    <ProtectedRoute requiredRole="admin">
      <Admin />
    </ProtectedRoute>
  }
/>

<Route
  path="/teams"
  element={
    <ProtectedRoute allowedRoles={['admin', 'project_manager']}>
      <Teams />
    </ProtectedRoute>
  }
/>
```

### Performance Optimizations

#### Database Optimizations
- **Indexes**: Automatic indexes on foreign keys and frequently queried columns
- **Query Optimization**: Selective field fetching with `.select()`
- **Pagination**: Implemented for large datasets
- **Connection Pooling**: Supabase handles connection management

#### Frontend Optimizations
- **React Query Caching**: Automatic caching with stale-while-revalidate
- **Optimistic Updates**: Immediate UI updates before server confirmation
- **Code Splitting**: Route-based code splitting with React.lazy()
- **Memoization**: Strategic use of useMemo and useCallback

### Error Handling

#### Database Error Handling
```typescript
try {
  const { data, error } = await supabase
    .from('table_name')
    .insert(data);

  if (error) throw error;
  return data;
} catch (error) {
  console.error('Database error:', error);
  toast({
    title: "Error",
    description: error.message,
    variant: "destructive"
  });
}
```

#### Global Error Boundaries
- React Error Boundaries for component-level errors
- Toast notifications for user-facing errors
- Console logging for debugging
- Graceful degradation for non-critical features

### Testing Strategy

#### Current Testing Status: ⚠️ NEEDS IMPLEMENTATION

**Recommended Testing Approach:**
```typescript
// Unit Tests (Jest + React Testing Library)
describe('TaskCreateSheet', () => {
  it('should create task with auto-generated ID', async () => {
    // Test implementation
  });
});

// Integration Tests (Cypress)
describe('Project Workflow', () => {
  it('should complete full project creation flow', () => {
    // E2E test implementation
  });
});

// Database Tests (Supabase Test Suite)
describe('RLS Policies', () => {
  it('should enforce project access restrictions', async () => {
    // Database test implementation
  });
});
```

### Deployment & DevOps

#### Current Deployment: ✅ CONFIGURED
- **Frontend**: Vercel deployment with automatic builds
- **Backend**: Supabase managed infrastructure
- **Database**: PostgreSQL with automatic backups
- **CDN**: Global edge network for static assets

#### Environment Configuration
```typescript
// Environment Variables
VITE_SUPABASE_URL=https://rgcxrksmdkzarcdlscpk.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
RESEND_API_KEY=re_... (for email notifications)
```

### Monitoring & Analytics

#### Current Status: ⚠️ BASIC IMPLEMENTATION

**Available Monitoring:**
- Supabase Dashboard: Database metrics and logs
- Vercel Analytics: Frontend performance metrics
- Browser DevTools: Client-side debugging

**Recommended Enhancements:**
- Application Performance Monitoring (APM)
- User behavior analytics
- Error tracking (Sentry)
- Custom business metrics dashboard

---

*This documentation reflects the current state of the RatioHub system as of the analysis date. The system demonstrates a solid foundation with most core workflows fully implemented and ready for production use.*
