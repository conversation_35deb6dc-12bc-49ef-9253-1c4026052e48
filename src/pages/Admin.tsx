import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminUserManagement } from '@/components/admin/AdminUserManagement';
import { SystemConfiguration } from '@/components/admin/SystemConfiguration';
import { ImportExport } from '@/components/admin/ImportExport';
import { ResourceManagement } from '@/components/resources/ResourceManagement';
import { FileManager } from '@/components/files/FileManager';
import { SecurityManager } from '@/components/admin/SecurityManager';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Users, Shield, Settings, Database, Upload, Lock } from 'lucide-react';

const Admin: React.FC = () => {
  return (
    <ProtectedRoute requiredRole="admin">
      <div className="container mx-auto p-6 space-y-8">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-foreground">Admin Panel</h1>
          <p className="text-muted-foreground">
            Manage users, security, and system settings for RatioHub.
          </p>
        </div>

        <Tabs defaultValue="users" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Config
            </TabsTrigger>
            <TabsTrigger value="resources" className="flex items-center gap-2">
              <Database className="w-4 h-4" />
              Resources
            </TabsTrigger>
            <TabsTrigger value="files" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Files
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Lock className="w-4 h-4" />
              Security
            </TabsTrigger>
            <TabsTrigger value="import" className="flex items-center gap-2">
              <Upload className="w-4 h-4" />
              Import
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users">
            <AdminUserManagement />
          </TabsContent>

          <TabsContent value="system">
            <SystemConfiguration />
          </TabsContent>

          <TabsContent value="resources">
            <ResourceManagement />
          </TabsContent>

          <TabsContent value="files">
            <FileManager />
          </TabsContent>

          <TabsContent value="security">
            <SecurityManager />
          </TabsContent>

          <TabsContent value="import">
            <ImportExport />
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  );
};

export default Admin;