import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { ProjectOverview } from '@/components/projects/ProjectOverview';
import { ProjectTasks } from '@/components/projects/ProjectTasks';
import { ProjectPhases } from '@/components/projects/ProjectPhases';
import { ProjectTeam } from '@/components/projects/ProjectTeam';
import { ProjectFiles } from '@/components/projects/ProjectFiles';
import { TaskCreateSheet } from '@/components/projects/TaskCreateSheet';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Plus, Target, Users, FileText, FolderOpen } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

const ProjectDetails = () => {
  const { id } = useParams<{ id: string }>();
  const [isTaskSheetOpen, setIsTaskSheetOpen] = useState(false);

  const { data: project, isLoading, refetch } = useQuery({
    queryKey: ['project', id],
    queryFn: async () => {
      if (!id) throw new Error('Project ID is required');
      
      console.log('ProjectDetails: Fetching project with ID:', id);
      
      try {
        // First get the basic project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', id)
          .single();
        
        if (projectError) throw projectError;
        if (!projectData) return null;
        
        // Get team data separately if team_id exists
        let teamData = null;
        if (projectData.team_id) {
          const { data: team } = await supabase
            .from('teams')
            .select('name')
            .eq('id', projectData.team_id)
            .single();
          teamData = team;
        }
        
        // Get creator profile separately
        let creatorProfile = null;
        if (projectData.created_by) {
          const { data: profile } = await supabase
            .from('profiles')
            .select('first_name, last_name')
            .eq('user_id', projectData.created_by)
            .single();
          creatorProfile = profile;
        }
        
        console.log('ProjectDetails: Query result:', { projectData, teamData, creatorProfile });
        
        return {
          ...projectData,
          teams: teamData,
          profiles: creatorProfile
        };
      } catch (error) {
        console.error('ProjectDetails: Project query failed:', error);
        throw error;
      }
    },
    enabled: !!id,
  });

  console.log('ProjectDetails: Current state:', { id, project, isLoading });

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div className="animate-pulse">
            <div className="h-8 bg-muted rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!project) {
    return (
      <DashboardLayout>
        <div className="space-y-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Project Not Found</h1>
            <p className="text-muted-foreground">
              The project you're looking for doesn't exist or you don't have access to it.
            </p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{project.name}</h1>
            <p className="text-muted-foreground">
              {project.project_id} • {project.description || 'No description provided'}
            </p>
          </div>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="phases" className="gap-2">
              <Target className="w-4 h-4" />
              Phases
            </TabsTrigger>
            <TabsTrigger value="tasks" className="gap-2">
              <Target className="w-4 h-4" />
              Tasks
            </TabsTrigger>
            <TabsTrigger value="team" className="gap-2">
              <Users className="w-4 h-4" />
              Team
            </TabsTrigger>
            <TabsTrigger value="files" className="gap-2">
              <FolderOpen className="w-4 h-4" />
              Files
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <ProjectOverview project={project} />
          </TabsContent>

          <TabsContent value="phases" className="space-y-6">
            <ProjectPhases projectId={id!} />
          </TabsContent>

          <TabsContent value="tasks" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium">All Tasks</h3>
                <p className="text-sm text-muted-foreground">
                  View all tasks across phases and milestones
                </p>
              </div>
              <Button onClick={() => setIsTaskSheetOpen(true)} className="gap-2">
                <Plus className="w-4 h-4" />
                New Task
              </Button>
            </div>
            <ProjectTasks projectId={id!} />
          </TabsContent>

          <TabsContent value="team" className="space-y-6">
            <ProjectTeam project={project} />
          </TabsContent>

          <TabsContent value="files" className="space-y-6">
            <ProjectFiles projectId={id!} />
          </TabsContent>
        </Tabs>

        {/* Task Creation Sheet */}
        <TaskCreateSheet
          open={isTaskSheetOpen}
          onOpenChange={setIsTaskSheetOpen}
          onTaskCreated={() => {
            refetch();
            setIsTaskSheetOpen(false);
          }}
          projectId={id!}
        />

      </div>
    </DashboardLayout>
  );
};

export default ProjectDetails;