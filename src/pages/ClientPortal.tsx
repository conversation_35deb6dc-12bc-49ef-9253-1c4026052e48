import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  Eye,
  DollarSign,
  Clock,
  Calendar,
  Users,
  TrendingUp,
  Target,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  FileText,
  MessageSquare,
  Download
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { useUserRole } from '@/hooks/useUserRole';
import { format, parseISO } from 'date-fns';
import { cn } from '@/lib/utils';

interface Project {
  id: string;
  name: string;
  project_id: string;
  description: string;
  status: string;
  budget: number;
  start_date: string;
  end_date: string;
  created_by: string;
  team_id: string;
}

interface ProjectProgress {
  totalTasks: number;
  completedTasks: number;
  totalHours: number;
  budgetUsed: number;
  teamMembers: number;
}

const ClientPortal = () => {
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const { user } = useAuth();
  const { userRoles } = useUserRole();

  // Check if user is a client (not admin/project_manager)
  const isClient = !userRoles.includes('admin') && !userRoles.includes('project_manager');

  // Fetch projects accessible to the client
  const { data: projects = [], isLoading: projectsLoading } = useQuery({
    queryKey: ['client-projects', user?.id],
    queryFn: async () => {
      let query = (supabase as any)
        .from('projects')
        .select(`
          *,
          teams(name),
          creator:profiles!created_by(first_name, last_name)
        `)
        .order('created_at', { ascending: false });

      // If client, only show projects they have access to
      if (isClient) {
        query = query.eq('client_id', user!.id);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data as Project[];
    },
    enabled: !!user,
  });

  // Fetch project progress for selected project
  const { data: projectProgress } = useQuery({
    queryKey: ['project-progress', selectedProject],
    queryFn: async () => {
      if (!selectedProject) return null;

      // Get tasks
      const { data: tasks } = await (supabase as any)
        .from('tasks')
        .select('id, status')
        .eq('project_id', selectedProject);

      // Get time entries
      const { data: timeEntries } = await (supabase as any)
        .from('time_entries')
        .select('duration_minutes, is_billable, hourly_rate')
        .eq('project_id', selectedProject)
        .not('end_time', 'is', null);

      // Get team members
      const { data: project } = await (supabase as any)
        .from('projects')
        .select('team_id')
        .eq('id', selectedProject)
        .single();

      let teamMemberCount = 0;
      if (project?.team_id) {
        const { data: teamMembers } = await (supabase as any)
          .from('team_members')
          .select('user_id')
          .eq('team_id', project.team_id);
        teamMemberCount = teamMembers?.length || 0;
      }

      const totalTasks = tasks?.length || 0;
      const completedTasks = tasks?.filter(task => task.status === 'completed').length || 0;
      const totalHours = timeEntries?.reduce((sum, entry) => sum + (entry.duration_minutes / 60), 0) || 0;
      const budgetUsed = timeEntries?.reduce((sum, entry) => {
        if (entry.is_billable && entry.hourly_rate) {
          return sum + ((entry.duration_minutes / 60) * entry.hourly_rate);
        }
        return sum;
      }, 0) || 0;

      return {
        totalTasks,
        completedTasks,
        totalHours,
        budgetUsed,
        teamMembers: teamMemberCount,
      } as ProjectProgress;
    },
    enabled: !!selectedProject,
  });

  // Fetch recent activity for selected project
  const { data: recentActivity = [] } = useQuery({
    queryKey: ['project-activity', selectedProject],
    queryFn: async () => {
      if (!selectedProject) return [];

      // Get recent tasks and milestones
      const { data: tasks } = await (supabase as any)
        .from('tasks')
        .select(`
          id, title, status, updated_at, created_at,
          creator:profiles!created_by(first_name, last_name)
        `)
        .eq('project_id', selectedProject)
        .order('updated_at', { ascending: false })
        .limit(10);

      const { data: milestones } = await (supabase as any)
        .from('milestones')
        .select('id, name, is_completed, updated_at, created_at')
        .eq('project_id', selectedProject)
        .order('updated_at', { ascending: false })
        .limit(5);

      // Combine and sort activities
      const activities = [
        ...(tasks?.map(task => ({
          id: task.id,
          type: 'task',
          title: task.title,
          status: task.status,
          date: task.updated_at,
          user: task.creator ? `${task.creator.first_name} ${task.creator.last_name}` : 'Unknown',
        })) || []),
        ...(milestones?.map(milestone => ({
          id: milestone.id,
          type: 'milestone',
          title: milestone.name,
          status: milestone.is_completed ? 'completed' : 'active',
          date: milestone.updated_at,
          user: 'Team',
        })) || [])
      ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      return activities.slice(0, 15);
    },
    enabled: !!selectedProject,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'secondary';
      case 'active': case 'in_progress': return 'default';
      case 'planning': return 'outline';
      case 'on_hold': return 'destructive';
      default: return 'outline';
    }
  };

  const getProgressPercentage = (progress?: ProjectProgress, budget?: number) => {
    if (!progress || !budget) return 0;
    return Math.min(100, (progress.budgetUsed / budget) * 100);
  };

  const getTaskCompletionPercentage = (progress?: ProjectProgress) => {
    if (!progress || progress.totalTasks === 0) return 0;
    return (progress.completedTasks / progress.totalTasks) * 100;
  };

  // If viewing project details
  if (selectedProject) {
    const project = projects.find(p => p.id === selectedProject);
    if (!project) return null;

    return (
      <DashboardLayout>
        <div className="container mx-auto p-6 space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => setSelectedProject(null)}>
              ← Back to Projects
            </Button>
          </div>

          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold">{project.name}</h1>
              <p className="text-muted-foreground">{project.description}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline">{project.project_id}</Badge>
                <Badge variant={getStatusColor(project.status)}>
                  {project.status.replace('_', ' ')}
                </Badge>
              </div>
            </div>
            <Button variant="outline" className="gap-2">
              <Download className="w-4 h-4" />
              Export Report
            </Button>
          </div>

          {/* Project Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Budget Used</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${projectProgress?.budgetUsed.toFixed(2) || '0.00'}
                </div>
                <Progress 
                  value={getProgressPercentage(projectProgress, project.budget)} 
                  className="mt-2" 
                />
                <p className="text-xs text-muted-foreground mt-1">
                  of ${project.budget?.toFixed(2) || '0.00'} budget
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Task Progress</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {projectProgress?.completedTasks || 0}/{projectProgress?.totalTasks || 0}
                </div>
                <Progress 
                  value={getTaskCompletionPercentage(projectProgress)} 
                  className="mt-2" 
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {getTaskCompletionPercentage(projectProgress).toFixed(1)}% complete
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Hours Logged</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {projectProgress?.totalHours.toFixed(1) || '0.0'}h
                </div>
                <p className="text-xs text-muted-foreground">
                  Total time tracked
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Team Size</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {projectProgress?.teamMembers || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Active team members
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Project Timeline & Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Project Timeline
                  </CardTitle>
                  <CardDescription>
                    Key milestones and project phases
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-primary rounded-full"></div>
                        <div>
                          <div className="font-medium">Project Start</div>
                          <div className="text-sm text-muted-foreground">
                            {format(parseISO(project.start_date), 'MMM d, yyyy')}
                          </div>
                        </div>
                      </div>
                      <CheckCircle className="w-5 h-5 text-success" />
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div>
                          <div className="font-medium">Current Phase</div>
                          <div className="text-sm text-muted-foreground">
                            {project.status.replace('_', ' ')}
                          </div>
                        </div>
                      </div>
                      <Badge variant="default">Active</Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-muted rounded-full"></div>
                        <div>
                          <div className="font-medium">Project End</div>
                          <div className="text-sm text-muted-foreground">
                            {project.end_date ? format(parseISO(project.end_date), 'MMM d, yyyy') : 'TBD'}
                          </div>
                        </div>
                      </div>
                      <Target className="w-5 h-5 text-muted-foreground" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Latest updates and progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {recentActivity.map((activity, index) => (
                    <div key={activity.id} className="flex gap-3">
                      <Avatar className="w-6 h-6 mt-1">
                        <AvatarFallback className="text-xs">
                          {activity.type === 'task' ? 'T' : 'M'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="text-sm">
                          <span className="font-medium">{activity.title}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {activity.status}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {activity.user} • {format(parseISO(activity.date), 'MMM d, h:mm a')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Main client portal view
  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              {isClient ? 'Client Portal' : 'Project Dashboard'}
            </h1>
            <p className="text-muted-foreground">
              {isClient 
                ? 'Real-time visibility into your projects and progress'
                : 'Overview of all projects and client visibility'
              }
            </p>
          </div>
          <Button variant="outline" className="gap-2">
            <FileText className="w-4 h-4" />
            View Reports
          </Button>
        </div>

        {/* Projects Grid */}
        {projectsLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground mt-2">Loading projects...</p>
          </div>
        ) : projects.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Eye className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No projects found</h3>
              <p className="text-muted-foreground">
                {isClient 
                  ? 'No projects have been assigned to you yet.'
                  : 'No projects available to display.'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {projects.map((project) => (
              <Card 
                key={project.id}
                className="hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => setSelectedProject(project.id)}
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg mb-1">{project.name}</CardTitle>
                      <Badge variant="outline" className="text-xs">
                        {project.project_id}
                      </Badge>
                    </div>
                    <Badge variant={getStatusColor(project.status)}>
                      {project.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  <CardDescription className="line-clamp-2">
                    {project.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Budget</span>
                      <span className="font-medium">${project.budget?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Timeline</span>
                      <span className="font-medium">
                        {format(parseISO(project.start_date), 'MMM d')} - 
                        {project.end_date ? format(parseISO(project.end_date), 'MMM d') : 'Ongoing'}
                      </span>
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">View Details</span>
                      <Eye className="w-4 h-4 text-muted-foreground" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ClientPortal;