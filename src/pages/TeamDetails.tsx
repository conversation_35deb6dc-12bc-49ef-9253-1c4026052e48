import React from 'react';
import { useParams } from 'react-router-dom';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { TeamDetailView } from '@/components/teams/TeamDetailView';

const TeamDetails = () => {
  const { id } = useParams<{ id: string }>();

  if (!id) {
    return (
      <DashboardLayout>
        <div className="text-center py-8">
          <h1 className="text-2xl font-bold text-destructive">Team not found</h1>
          <p className="text-muted-foreground">The team you're looking for doesn't exist.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        <TeamDetailView teamId={id} />
      </div>
    </DashboardLayout>
  );
};

export default TeamDetails;