import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  FolderOpen,
  Users,
  Clock,
  BarChart3,
  Settings,
  HeadphonesIcon,
  Shield,
  Building2,
  Home,
  MessageCircle,
  Bell,
  Calendar as CalendarIcon,
  CreditCard,
  Eye
} from 'lucide-react';
import { useUserRole } from '@/hooks/useUserRole';
import {
  Sidebar as SidebarComponent,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  SidebarHeader,
  useSidebar,
} from '@/components/ui/sidebar';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  requiredRoles?: string[];
}

const navigation: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Projects', href: '/projects', icon: FolderOpen },
  { name: 'Teams', href: '/teams', icon: Users, requiredRoles: ['admin', 'project_manager'] },
  { name: 'Time Tracking', href: '/time-tracking', icon: Clock },
  { name: 'Support Tickets', href: '/support-tickets', icon: HeadphonesIcon },
  { name: 'Communication', href: '/communication', icon: MessageCircle },
  { name: 'Notifications', href: '/notifications', icon: Bell },
  { name: 'Calendar', href: '/calendar', icon: CalendarIcon },
  { name: 'Billing', href: '/billing', icon: CreditCard, requiredRoles: ['admin', 'project_manager'] },
  { name: 'Reports', href: '/reports', icon: BarChart3, requiredRoles: ['admin', 'project_manager'] },
  { name: 'Client Portal', href: '/client-portal', icon: Eye },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export const Sidebar: React.FC = () => {
  const { state } = useSidebar();
  const collapsed = state === 'collapsed';
  const location = useLocation();
  const { userRoles, isAdmin, isProjectManager } = useUserRole();
  const currentPath = location.pathname;

  const hasAccess = (item: NavigationItem): boolean => {
    if (!item.requiredRoles) return true;
    return item.requiredRoles.some(role => userRoles.includes(role));
  };

  const isActive = (path: string): boolean => {
    if (path === '/') return currentPath === '/';
    return currentPath.startsWith(path);
  };

  const getNavClassName = (active: boolean): string => {
    return active 
      ? "bg-primary text-primary-foreground hover:bg-primary/90"
      : "hover:bg-accent hover:text-accent-foreground";
  };

  return (
    <SidebarComponent 
      className={`${collapsed ? "w-14" : "w-64"} transition-all duration-300`}
      collapsible="icon"
    >
      <SidebarHeader className="p-4 border-b border-sidebar-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <Building2 className="w-5 h-5 text-primary-foreground" />
          </div>
          {!collapsed && (
            <div>
              <h2 className="font-bold text-lg text-sidebar-foreground">RatioHub</h2>
              <p className="text-xs text-sidebar-foreground/60">Project Management</p>
            </div>
          )}
        </div>
        <SidebarTrigger className="ml-auto" />
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigation.filter(hasAccess).map((item) => (
                <SidebarMenuItem key={item.name}>
                  <SidebarMenuButton asChild>
                    <NavLink 
                      to={item.href} 
                      className={getNavClassName(isActive(item.href))}
                    >
                      <item.icon className="w-4 h-4" />
                      {!collapsed && <span>{item.name}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {(isAdmin || isProjectManager) && (
          <SidebarGroup>
            <SidebarGroupLabel>Management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <NavLink 
                      to="/admin" 
                      className={getNavClassName(isActive('/admin'))}
                    >
                      <Shield className="w-4 h-4" />
                      {!collapsed && <span>Admin Panel</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>
    </SidebarComponent>
  );
};