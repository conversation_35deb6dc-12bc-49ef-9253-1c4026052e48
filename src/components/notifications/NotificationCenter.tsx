import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Bell, Mail, Smartphone, Settings, Users, AlertCircle } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  user_id: string;
  read: boolean;
  created_at: string;
  action_url?: string;
}

interface NotificationSettings {
  email_enabled: boolean;
  push_enabled: boolean;
  project_updates: boolean;
  task_assignments: boolean;
  deadline_reminders: boolean;
  team_mentions: boolean;
}

export const NotificationCenter = () => {
  const queryClient = useQueryClient();
  const [settings, setSettings] = useState<NotificationSettings>({
    email_enabled: true,
    push_enabled: true,
    project_updates: true,
    task_assignments: true,
    deadline_reminders: true,
    team_mentions: true
  });

  const { data: notifications = [], isLoading } = useQuery({
    queryKey: ['notifications'],
    queryFn: async () => {
      try {
        const { data: user } = await supabase.auth.getUser();
        if (!user.user) return [];

        // Get activity logs as notifications
        const { data: activities } = await (supabase as any)
          .from('activity_logs')
          .select(`
            *,
            profiles:user_id (
              first_name,
              last_name
            )
          `)
          .order('created_at', { ascending: false })
          .limit(20);

        // Get assigned tasks as notifications
        const { data: assignedTasks } = await (supabase as any)
          .from('tasks')
          .select(`
            *,
            projects (
              name,
              project_id
            )
          `)
          .eq('assigned_to', user.user.id)
          .order('created_at', { ascending: false })
          .limit(10);

        // Get support tickets for the user
        const { data: tickets } = await (supabase as any)
          .from('support_tickets')
          .select('*')
          .eq('created_by', user.user.id)
          .order('created_at', { ascending: false })
          .limit(10);

        // Format notifications from different sources
        const formattedNotifications: Notification[] = [
          ...(activities?.map((activity: any) => ({
            id: `activity_${activity.id}`,
            title: `${activity.profiles?.first_name || 'Someone'} ${activity.action}`,
            message: `${activity.entity_type} was updated`,
            type: 'info' as const,
            user_id: activity.user_id,
            read: false,
            created_at: activity.created_at
          })) || []),
          ...(assignedTasks?.map((task: any) => ({
            id: `task_${task.id}`,
            title: 'New Task Assignment',
            message: `You've been assigned to "${task.title}" in ${task.projects?.name}`,
            type: 'info' as const,
            user_id: task.assigned_to,
            read: false,
            created_at: task.created_at,
            action_url: `/projects/${task.project_id}`
          })) || []),
          ...(tickets?.map((ticket: any) => ({
            id: `ticket_${ticket.id}`,
            title: 'Support Ticket Update',
            message: `Ticket ${ticket.ticket_id}: ${ticket.title} - ${ticket.status}`,
            type: ticket.priority === 'high' ? 'warning' as const : 'info' as const,
            user_id: ticket.created_by,
            read: false,
            created_at: ticket.updated_at,
            action_url: `/support-tickets`
          })) || [])
        ];

        return formattedNotifications.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      } catch (error) {
        console.error('Error fetching notifications:', error);
        return [];
      }
    },
    refetchInterval: 30000 // Refetch every 30 seconds for real-time updates
  });

  const markAsReadMutation = useMutation({
    mutationFn: async (notificationId: string) => {
      // In a real implementation, this would update the read status in the database
      // For now, we'll just simulate the API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return notificationId;
    },
    onSuccess: () => {
      toast.success('Notification marked as read');
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    }
  });

  const updateSettingsMutation = useMutation({
    mutationFn: async (newSettings: NotificationSettings) => {
      // In a real implementation, this would save to user preferences table
      await new Promise(resolve => setTimeout(resolve, 1000));
      return newSettings;
    },
    onSuccess: (newSettings) => {
      setSettings(newSettings);
      toast.success('Notification settings updated successfully');
    }
  });

  const sendTestNotificationMutation = useMutation({
    mutationFn: async () => {
      // In a real implementation, this would create a test notification
      await new Promise(resolve => setTimeout(resolve, 1000));
      return true;
    },
    onSuccess: () => {
      toast.success('Test notification sent - check your notification channels');
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    }
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'error': return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'success': return <AlertCircle className="h-5 w-5 text-green-500" />;
      default: return <Bell className="h-5 w-5 text-blue-500" />;
    }
  };

  const getNotificationBadgeVariant = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return 'secondary';
      case 'error': return 'destructive';
      case 'success': return 'default';
      default: return 'outline';
    }
  };

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Notification Center</h2>
          <p className="text-muted-foreground">
            Manage your notifications and communication preferences
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {unreadCount} unread
          </Badge>
          <Button 
            onClick={() => sendTestNotificationMutation.mutate()}
            disabled={sendTestNotificationMutation.isPending}
          >
            Send Test
          </Button>
        </div>
      </div>

      <Tabs defaultValue="notifications" className="space-y-4">
        <TabsList>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="templates">Email Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Recent Notifications
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map(i => (
                    <div key={i} className="h-16 bg-muted rounded animate-pulse" />
                  ))}
                </div>
              ) : notifications.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No notifications yet</p>
                  <p className="text-xs">New notifications from your projects and tasks will appear here</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {notifications.map((notification) => (
                    <div 
                      key={notification.id}
                      className={`flex items-start gap-4 p-4 rounded-lg border ${
                        notification.read ? 'bg-muted/30' : 'bg-background'
                      }`}
                    >
                      {getNotificationIcon(notification.type)}
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{notification.title}</p>
                          <div className="flex items-center gap-2">
                            <Badge variant={getNotificationBadgeVariant(notification.type)}>
                              {notification.type}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(notification.created_at).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {notification.message}
                        </p>
                        {!notification.read && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => markAsReadMutation.mutate(notification.id)}
                          >
                            Mark as Read
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Delivery Methods</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        <Label>Email Notifications</Label>
                      </div>
                      <Switch
                        checked={settings.email_enabled}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, email_enabled: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4" />
                        <Label>Push Notifications</Label>
                      </div>
                      <Switch
                        checked={settings.push_enabled}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, push_enabled: checked }))
                        }
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Notification Types</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>Project Updates</Label>
                      <Switch
                        checked={settings.project_updates}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, project_updates: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Task Assignments</Label>
                      <Switch
                        checked={settings.task_assignments}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, task_assignments: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Deadline Reminders</Label>
                      <Switch
                        checked={settings.deadline_reminders}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, deadline_reminders: checked }))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Team Mentions</Label>
                      <Switch
                        checked={settings.team_mentions}
                        onCheckedChange={(checked) =>
                          setSettings(prev => ({ ...prev, team_mentions: checked }))
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button 
                  onClick={() => updateSettingsMutation.mutate(settings)}
                  disabled={updateSettingsMutation.isPending}
                >
                  {updateSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Templates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Email templates are managed in the System Configuration section.
                </p>
                <Button variant="outline">
                  Go to System Config
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};