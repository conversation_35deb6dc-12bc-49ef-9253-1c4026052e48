import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserPlus, UserMinus, Crown, User } from 'lucide-react';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface TeamMemberManagementProps {
  teamId: string;
  onRefresh: () => void;
}

export const TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({
  teamId,
  onRefresh
}) => {
  const { toast } = useToast();
  const [selectedUserId, setSelectedUserId] = useState<string>('');

  // Fetch team members
  const { data: teamMembers, refetch: refetchMembers } = useQuery({
    queryKey: ['teamMembers', teamId],
    queryFn: async () => {
      try {
        // Get team members first
        const { data: members, error: membersError } = await supabase
          .from('team_members')
          .select('id, user_id, role, joined_at')
          .eq('team_id', teamId);
        
        if (membersError) throw membersError;
        
        if (!members || members.length === 0) {
          return [];
        }
        
        // Get profiles for the members
        const userIds = members.map(m => m.user_id);
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('user_id, first_name, last_name, email, avatar_url')
          .in('user_id', userIds);
        
        if (profilesError) throw profilesError;
        
        // Combine the data
        const combinedData = members.map(member => {
          const profile = profiles?.find(p => p.user_id === member.user_id);
          return {
            ...member,
            profiles: profile
          };
        });
        
        return combinedData || [];
      } catch (error) {
        console.warn('Team members query failed:', error);
        return [];
      }
    },
  });

  // Fetch available users (not in team)
  const { data: availableUsers } = useQuery({
    queryKey: ['availableUsers', teamId],
    queryFn: async () => {
      try {
        // Get existing team member user IDs
        const { data: existingMembers } = await supabase
          .from('team_members')
          .select('user_id')
          .eq('team_id', teamId);
        
        const existingUserIds = existingMembers?.map(m => m.user_id) || [];
        
        // Get all active users not in the team
        let query = supabase
          .from('profiles')
          .select('user_id, first_name, last_name, email')
          .eq('is_active', true)
          .order('first_name');
        
        if (existingUserIds.length > 0) {
          query = query.not('user_id', 'in', `(${existingUserIds.join(',')})`);
        }
        
        const { data, error } = await query;
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.warn('Available users query failed:', error);
        return [];
      }
    },
  });

  // Fetch pending invitations for this team
  const { data: pendingInvitations = [] } = useQuery({
    queryKey: ['team-invitations', teamId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('user_invitations')
        .select('*')
        .contains('team_ids', [teamId])
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString());

      if (error) throw error;
      return data || [];
    },
  });

  const addMemberMutation = useMutation({
    mutationFn: async (userId: string) => {
      const { error } = await (supabase as any)
        .from('team_members')
        .insert({
          team_id: teamId,
          user_id: userId,
          role: 'member',
        });
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Member added successfully',
        description: 'The user has been added to the team.',
      });
      setSelectedUserId('');
      refetchMembers();
      onRefresh();
    },
    onError: (error: any) => {
      toast({
        title: 'Error adding member',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const removeMemberMutation = useMutation({
    mutationFn: async (memberId: string) => {
      const { error } = await (supabase as any)
        .from('team_members')
        .delete()
        .eq('id', memberId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Member removed successfully',
        description: 'The user has been removed from the team.',
      });
      refetchMembers();
      onRefresh();
    },
    onError: (error: any) => {
      toast({
        title: 'Error removing member',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: async ({ memberId, role }: { memberId: string; role: string }) => {
      const { error } = await (supabase as any)
        .from('team_members')
        .update({ role })
        .eq('id', memberId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Role updated successfully',
        description: 'The member role has been updated.',
      });
      refetchMembers();
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating role',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="w-5 h-5" />
          Team Members ({teamMembers?.length || 0})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Add Member */}
        <div className="flex gap-2">
          <Select value={selectedUserId} onValueChange={setSelectedUserId}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Select user to add..." />
            </SelectTrigger>
            <SelectContent>
              {availableUsers?.map((user: any) => (
                <SelectItem key={user.user_id} value={user.user_id}>
                  {user.first_name} {user.last_name} ({user.email})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            onClick={() => addMemberMutation.mutate(selectedUserId)}
            disabled={!selectedUserId || addMemberMutation.isPending}
            className="gap-2"
          >
            <UserPlus className="w-4 h-4" />
            Add Member
          </Button>
        </div>

        {/* Members List */}
        <div className="space-y-4">
          {teamMembers?.map((member: any) => (
            <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarFallback>
                    {member.profiles?.first_name?.charAt(0)}
                    {member.profiles?.last_name?.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">
                    {member.profiles?.first_name} {member.profiles?.last_name}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {member.profiles?.email}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Joined: {member.joined_at ? new Date(member.joined_at).toLocaleDateString() : 'Unknown'}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Select
                  value={member.role}
                  onValueChange={(role) => 
                    updateRoleMutation.mutate({ memberId: member.id, role })
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="member">Member</SelectItem>
                    <SelectItem value="lead">Lead</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>

                <Badge variant={member.role === 'admin' ? 'default' : 'secondary'}>
                  {member.role === 'admin' && <Crown className="w-3 h-3 mr-1" />}
                  {member.role}
                </Badge>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeMemberMutation.mutate(member.id)}
                  disabled={removeMemberMutation.isPending}
                  className="text-destructive hover:text-destructive"
                >
                  <UserMinus className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
          
          {/* Pending invitations */}
          {pendingInvitations.map((invitation: any) => (
            <div key={invitation.id} className="flex items-center justify-between p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarFallback className="bg-orange-100 text-orange-600">
                    {invitation.email[0].toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{invitation.email}</div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      Invitation Pending
                    </Badge>
                    <div className="text-xs text-muted-foreground">
                      Expires {format(new Date(invitation.expires_at), 'MMM dd, yyyy')}
                    </div>
                  </div>
                </div>
              </div>
              <Badge variant="outline">{invitation.role.replace('_', ' ')}</Badge>
            </div>
          ))}
          
          {(!teamMembers || teamMembers.length === 0) && pendingInvitations.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No team members yet. Add some members to get started.
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};