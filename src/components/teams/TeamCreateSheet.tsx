import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const teamSchema = z.object({
  name: z.string().min(1, 'Team name is required'),
  description: z.string().optional(),
  member_ids: z.array(z.string()).optional(),
});

type TeamFormData = z.infer<typeof teamSchema>;

interface TeamCreateSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTeamCreated: () => void;
}

export const TeamCreateSheet: React.FC<TeamCreateSheetProps> = ({
  open,
  onOpenChange,
  onTeamCreated,
}) => {
  const { toast } = useToast();

  const form = useForm<TeamFormData>({
    resolver: zodResolver(teamSchema),
    defaultValues: {
      name: '',
      description: '',
      member_ids: [],
    },
  });

  // Fetch users for team member selection
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      try {
        const { data, error } = await (supabase as any)
          .from('profiles')
          .select('user_id, first_name, last_name, email')
          .eq('is_active', true)
          .order('first_name');
        
        if (error) throw error;
        return data || [];
      } catch (error) {
        console.warn('Users query failed:', error);
        return [];
      }
    },
  });

  const createTeamMutation = useMutation({
    mutationFn: async (data: TeamFormData) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      // Create team
      const teamData = {
        name: data.name,
        description: data.description || null,
        created_by: user.user.id,
        is_active: true,
      };

      const { data: team, error: teamError } = await (supabase as any)
        .from('teams')
        .insert(teamData)
        .select()
        .single();

      if (teamError) throw teamError;

      // Add team members if selected
      if (data.member_ids && data.member_ids.length > 0) {
        const memberData = data.member_ids.map(userId => ({
          team_id: team.id,
          user_id: userId,
          role: 'member',
        }));

        const { error: membersError } = await (supabase as any)
          .from('team_members')
          .insert(memberData);

        if (membersError) throw membersError;
      }

      return team;
    },
    onSuccess: (team: any) => {
      toast({
        title: 'Team created successfully',
        description: `${team?.name} has been created.`,
      });
      form.reset();
      onTeamCreated();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating team',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: TeamFormData) => {
    createTeamMutation.mutate(data);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[500px] max-w-[90vw] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Create New Team</SheetTitle>
          <SheetDescription>
            Create a new team and assign members to collaborate on projects.
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Team Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter team name..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe the team's purpose and responsibilities..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="member_ids"
                render={() => (
                  <FormItem>
                    <FormLabel>Team Members</FormLabel>
                    <div className="max-h-64 overflow-y-auto border rounded-md p-4 space-y-2">
                      {users?.map((user: any) => (
                        <FormField
                          key={user.user_id}
                          control={form.control}
                          name="member_ids"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={user.user_id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(user.user_id)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, user.user_id])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== user.user_id
                                            )
                                          )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  {user.first_name} {user.last_name} ({user.email})
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Actions */}
              <div className="flex justify-end space-x-2 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={createTeamMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={createTeamMutation.isPending}
                  className="gap-2"
                >
                  {createTeamMutation.isPending && (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  )}
                  Create Team
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
};