import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { 
  FileText, 
  Download, 
  Send, 
  DollarSign, 
  Calendar,
  Clock,
  User,
  Building,
  Calculator
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';

interface Invoice {
  id: string;
  invoice_number: string;
  client_name: string;
  project_name: string;
  amount: number;
  tax_amount: number;
  total_amount: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  issue_date: string;
  due_date: string;
  items: InvoiceItem[];
}

interface InvoiceItem {
  id: string;
  description: string;
  hours: number;
  rate: number;
  amount: number;
}

interface InvoiceTemplate {
  id: string;
  name: string;
  company_name: string;
  company_address: string;
  tax_rate: number;
  payment_terms: string;
  notes: string;
}

export const InvoiceManager = () => {
  const queryClient = useQueryClient();
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [generatingInvoice, setGeneratingInvoice] = useState(false);
  const [invoiceProgress, setInvoiceProgress] = useState(0);

  const { data: invoices = [], isLoading } = useQuery({
    queryKey: ['invoices'],
    queryFn: async () => {
      // Mock invoice data
      return [
        {
          id: '1',
          invoice_number: 'INV-2024-001',
          client_name: 'Acme Corporation',
          project_name: 'Website Redesign',
          amount: 12500.00,
          tax_amount: 1250.00,
          total_amount: 13750.00,
          status: 'sent' as const,
          issue_date: '2024-01-15',
          due_date: '2024-02-15',
          items: [
            {
              id: '1',
              description: 'Frontend Development',
              hours: 80,
              rate: 125,
              amount: 10000
            },
            {
              id: '2',
              description: 'Backend Integration',
              hours: 20,
              rate: 125,
              amount: 2500
            }
          ]
        },
        {
          id: '2',
          invoice_number: 'INV-2024-002',
          client_name: 'Tech Startup Inc',
          project_name: 'Mobile App Development',
          amount: 8000.00,
          tax_amount: 800.00,
          total_amount: 8800.00,
          status: 'paid' as const,
          issue_date: '2024-01-20',
          due_date: '2024-02-20',
          items: [
            {
              id: '3',
              description: 'React Native Development',
              hours: 64,
              rate: 125,
              amount: 8000
            }
          ]
        }
      ] as Invoice[];
    }
  });

  const { data: projects = [] } = useQuery({
    queryKey: ['projects-for-billing'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('projects')
        .select('id, name, client_id, budget, hourly_rate')
        .eq('is_billable', true);
      
      if (error) throw error;
      return data || [];
    }
  });

  const { data: template } = useQuery({
    queryKey: ['invoice-template'],
    queryFn: async () => {
      // Mock template data
      return {
        id: '1',
        name: 'Standard Invoice',
        company_name: 'RatioHub Solutions',
        company_address: '123 Business St, City, State 12345',
        tax_rate: 10,
        payment_terms: 'Net 30',
        notes: 'Thank you for your business!'
      } as InvoiceTemplate;
    }
  });

  const generateInvoiceMutation = useMutation({
    mutationFn: async (projectId: string) => {
      setGeneratingInvoice(true);
      setInvoiceProgress(0);

      // Simulate invoice generation process
      const steps = [
        'Fetching time entries...',
        'Calculating billable hours...',
        'Applying rates and taxes...',
        'Generating PDF...',
        'Saving invoice...'
      ];

      for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setInvoiceProgress(((i + 1) / steps.length) * 100);
      }

      // Mock invoice generation
      const newInvoice: Invoice = {
        id: Date.now().toString(),
        invoice_number: `INV-2024-${String(invoices.length + 1).padStart(3, '0')}`,
        client_name: 'New Client',
        project_name: 'Selected Project',
        amount: 5000,
        tax_amount: 500,
        total_amount: 5500,
        status: 'draft',
        issue_date: new Date().toISOString().split('T')[0],
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        items: [
          {
            id: '1',
            description: 'Development Work',
            hours: 40,
            rate: 125,
            amount: 5000
          }
        ]
      };

      setGeneratingInvoice(false);
      setInvoiceProgress(0);
      return newInvoice;
    },
    onSuccess: () => {
      toast.success('Invoice generated successfully');
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
    },
    onError: () => {
      toast.error('Failed to generate invoice');
      setGeneratingInvoice(false);
      setInvoiceProgress(0);
    }
  });

  const sendInvoiceMutation = useMutation({
    mutationFn: async (invoiceId: string) => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      return invoiceId;
    },
    onSuccess: () => {
      toast.success('Invoice sent successfully');
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
    },
    onError: () => {
      toast.error('Failed to send invoice');
    }
  });

  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'paid': return 'default';
      case 'sent': return 'secondary';
      case 'overdue': return 'destructive';
      case 'draft': return 'outline';
      default: return 'outline';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold tracking-tight">Invoice Manager</h2>
        <div className="flex gap-4">
          <Select value={selectedProject} onValueChange={setSelectedProject}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {projects.map(project => (
                <SelectItem key={project.id} value={project.id}>{project.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button 
            onClick={() => generateInvoiceMutation.mutate(selectedProject)}
            disabled={!selectedProject || generatingInvoice}
          >
            <FileText className="h-4 w-4 mr-2" />
            Generate Invoice
          </Button>
        </div>
      </div>

      {generatingInvoice && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Generating invoice...</span>
                <span>{Math.round(invoiceProgress)}%</span>
              </div>
              <Progress value={invoiceProgress} />
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="invoices" className="space-y-4">
        <TabsList>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="invoices" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <DollarSign className="h-5 w-5" />
                  Total Revenue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(invoices.reduce((sum, inv) => sum + inv.total_amount, 0))}
                </div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Clock className="h-5 w-5" />
                  Pending
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(
                    invoices
                      .filter(inv => inv.status === 'sent')
                      .reduce((sum, inv) => sum + inv.total_amount, 0)
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  {invoices.filter(inv => inv.status === 'sent').length} invoices
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Calendar className="h-5 w-5" />
                  Overdue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(
                    invoices
                      .filter(inv => inv.status === 'overdue')
                      .reduce((sum, inv) => sum + inv.total_amount, 0)
                  )}
                </div>
                <p className="text-xs text-muted-foreground">
                  {invoices.filter(inv => inv.status === 'overdue').length} invoices
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Calculator className="h-5 w-5" />
                  This Month
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(
                    invoices
                      .filter(inv => new Date(inv.issue_date).getMonth() === new Date().getMonth())
                      .reduce((sum, inv) => sum + inv.total_amount, 0)
                  )}
                </div>
                <p className="text-xs text-muted-foreground">Current month</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {invoices.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <p className="font-medium">{invoice.invoice_number}</p>
                        <Badge variant={getStatusColor(invoice.status)}>
                          {invoice.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {invoice.client_name} • {invoice.project_name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Due: {new Date(invoice.due_date).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right space-y-1">
                      <p className="font-medium">{formatCurrency(invoice.total_amount)}</p>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="h-3 w-3" />
                        </Button>
                        {invoice.status === 'draft' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => sendInvoiceMutation.mutate(invoice.id)}
                            disabled={sendInvoiceMutation.isPending}
                          >
                            <Send className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Billing Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium mb-2">Payment Status Distribution</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Paid</span>
                        <span>{invoices.filter(i => i.status === 'paid').length}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Sent</span>
                        <span>{invoices.filter(i => i.status === 'sent').length}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Draft</span>
                        <span>{invoices.filter(i => i.status === 'draft').length}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Average Payment Time</h4>
                    <div className="text-2xl font-bold">18 days</div>
                    <p className="text-sm text-muted-foreground">Average collection period</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Invoice Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="company-name">Company Name</Label>
                    <Input id="company-name" defaultValue={template?.company_name} />
                  </div>
                  <div>
                    <Label htmlFor="company-address">Company Address</Label>
                    <Textarea id="company-address" defaultValue={template?.company_address} />
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="tax-rate">Tax Rate (%)</Label>
                    <Input 
                      id="tax-rate" 
                      type="number" 
                      defaultValue={template?.tax_rate} 
                    />
                  </div>
                  <div>
                    <Label htmlFor="payment-terms">Payment Terms</Label>
                    <Select defaultValue={template?.payment_terms}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Net 15">Net 15</SelectItem>
                        <SelectItem value="Net 30">Net 30</SelectItem>
                        <SelectItem value="Net 45">Net 45</SelectItem>
                        <SelectItem value="Due on receipt">Due on receipt</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              <div>
                <Label htmlFor="notes">Default Notes</Label>
                <Textarea id="notes" defaultValue={template?.notes} />
              </div>
              <div className="flex justify-end">
                <Button>Save Settings</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};