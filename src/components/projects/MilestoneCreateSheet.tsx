import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Loader2 } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const milestoneSchema = z.object({
  name: z.string().min(1, 'Milestone name is required'),
  description: z.string().optional(),
  due_date: z.date().optional(),
});

type MilestoneFormData = z.infer<typeof milestoneSchema>;

interface MilestoneCreateSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onMilestoneCreated: () => void;
  projectId: string;
  phaseId?: string;
}

export const MilestoneCreateSheet: React.FC<MilestoneCreateSheetProps> = ({
  open,
  onOpenChange,
  onMilestoneCreated,
  projectId,
  phaseId,
}) => {
  const { toast } = useToast();

  const form = useForm<MilestoneFormData>({
    resolver: zodResolver(milestoneSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  const createMilestoneMutation = useMutation({
    mutationFn: async (data: MilestoneFormData) => {
      const milestoneData = {
        name: data.name,
        description: data.description || null,
        due_date: data.due_date?.toISOString().split('T')[0] || null,
        project_id: projectId,
        phase_id: phaseId || null,
        is_completed: false,
      };

      const { data: milestone, error } = await (supabase as any)
        .from('milestones')
        .insert(milestoneData)
        .select()
        .single();

      if (error) throw error;
      return milestone;
    },
    onSuccess: (milestone: any) => {
      toast({
        title: 'Milestone created successfully',
        description: `${milestone?.name} has been created.`,
      });
      form.reset();
      onMilestoneCreated();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating milestone',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const onSubmit = (data: MilestoneFormData) => {
    createMilestoneMutation.mutate(data);
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-[400px] max-w-[90vw] flex flex-col">
        <SheetHeader className="flex-shrink-0">
          <SheetTitle>Create New Milestone</SheetTitle>
          <SheetDescription>
            Add a new milestone to track project progress.
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto space-y-6 mt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Milestone Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter milestone name..." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe the milestone objectives..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="due_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick due date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Actions */}
            </form>
          </Form>
        </div>

        {/* Actions - Fixed Footer */}
        <div className="flex-shrink-0 flex justify-end space-x-2 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={createMilestoneMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={createMilestoneMutation.isPending}
            className="gap-2"
          >
            {createMilestoneMutation.isPending && (
              <Loader2 className="h-4 w-4 animate-spin" />
            )}
            Create Milestone
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};