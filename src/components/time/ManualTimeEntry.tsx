import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';

import { useAuth } from '@/components/auth/AuthContext';
import { Sheet, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';
import { format, parseISO } from 'date-fns';
import { CalendarIcon, Clock, Save } from 'lucide-react';

interface ManualTimeEntryProps {
  isOpen: boolean;
  onClose: () => void;
  editEntry?: {
    id: string;
    start_time: string;
    end_time: string | null;
    duration_minutes: number | null;
    description: string | null;
    is_billable: boolean;
    hourly_rate: number | null;
    project_id: string;
    task_id: string | null;
  } | null;
}

export const ManualTimeEntry: React.FC<ManualTimeEntryProps> = ({
  isOpen,
  onClose,
  editEntry,
}) => {
  const [selectedProject, setSelectedProject] = useState('');
  const [selectedTask, setSelectedTask] = useState('');
  const [description, setDescription] = useState('');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');
  const [isBillable, setIsBillable] = useState(true);
  const [hourlyRate, setHourlyRate] = useState<number>(0);

  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Reset form when opening/closing
  useEffect(() => {
    if (isOpen) {
      if (editEntry) {
        setSelectedProject(editEntry.project_id);
        setSelectedTask(editEntry.task_id || '');
        setDescription(editEntry.description || '');
        setSelectedDate(parseISO(editEntry.start_time));
        setStartTime(format(parseISO(editEntry.start_time), 'HH:mm'));
        if (editEntry.end_time) {
          setEndTime(format(parseISO(editEntry.end_time), 'HH:mm'));
        }
        setIsBillable(editEntry.is_billable);
        setHourlyRate(editEntry.hourly_rate || 0);
      } else {
        setSelectedProject('');
        setSelectedTask('');
        setDescription('');
        setSelectedDate(new Date());
        setStartTime('09:00');
        setEndTime('17:00');
        setIsBillable(true);
        setHourlyRate(0);
      }
    }
  }, [isOpen, editEntry]);

  // Fetch projects
  const { data: projects = [] } = useQuery({
    queryKey: ['user-projects'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('projects')
        .select('id, name, project_id, hourly_rate, is_billable')
        .order('name');
      
      if (error) throw error;
      return data;
    },
    enabled: !!user && isOpen,
  });

  // Fetch tasks for selected project
  const { data: tasks = [] } = useQuery({
    queryKey: ['project-tasks', selectedProject],
    queryFn: async () => {
      if (!selectedProject) return [];
      
      const { data, error } = await (supabase as any)
        .from('tasks')
        .select('id, title, task_id')
        .eq('project_id', selectedProject)
        .order('title');
      
      if (error) throw error;
      return data;
    },
    enabled: !!selectedProject && isOpen,
  });

  // Update project-specific settings
  useEffect(() => {
    const project = projects.find(p => p.id === selectedProject);
    if (project && !editEntry) {
      setIsBillable(project.is_billable);
      setHourlyRate(project.hourly_rate || 0);
    }
  }, [selectedProject, projects, editEntry]);

  const saveEntryMutation = useMutation({
    mutationFn: async () => {
      if (!selectedProject) throw new Error('Please select a project');
      
      const startDateTime = new Date(selectedDate);
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      startDateTime.setHours(startHours, startMinutes, 0, 0);
      
      const endDateTime = new Date(selectedDate);
      const [endHours, endMinutes] = endTime.split(':').map(Number);
      endDateTime.setHours(endHours, endMinutes, 0, 0);
      
      // Handle overnight entries
      if (endDateTime <= startDateTime) {
        endDateTime.setDate(endDateTime.getDate() + 1);
      }
      
      const durationMinutes = Math.floor((endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60));
      
      if (durationMinutes <= 0) {
        throw new Error('End time must be after start time');
      }

      const entryData = {
        user_id: user!.id,
        project_id: selectedProject,
        task_id: selectedTask || null,
        description: description.trim() || null,
        start_time: startDateTime.toISOString(),
        end_time: endDateTime.toISOString(),
        duration_minutes: durationMinutes,
        is_billable: isBillable,
        hourly_rate: isBillable ? hourlyRate : null,
      };

      if (editEntry) {
        const { data, error } = await (supabase as any)
          .from('time_entries')
          .update(entryData)
          .eq('id', editEntry.id)
          .select()
          .single();
        
        if (error) throw error;
        return data;
      } else {
        const { data, error } = await (supabase as any)
          .from('time_entries')
          .insert(entryData)
          .select()
          .single();
        
        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['time-entries'] });
      toast({
        title: editEntry ? 'Entry updated' : 'Entry created',
        description: `Time entry has been ${editEntry ? 'updated' : 'created'} successfully.`,
      });
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: `Error ${editEntry ? 'updating' : 'creating'} entry`,
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const calculateDuration = () => {
    const startDateTime = new Date(selectedDate);
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    startDateTime.setHours(startHours, startMinutes, 0, 0);
    
    const endDateTime = new Date(selectedDate);
    const [endHours, endMinutes] = endTime.split(':').map(Number);
    endDateTime.setHours(endHours, endMinutes, 0, 0);
    
    // Handle overnight entries
    if (endDateTime <= startDateTime) {
      endDateTime.setDate(endDateTime.getDate() + 1);
    }
    
    const durationMinutes = Math.floor((endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60));
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    
    return `${hours}h ${minutes}m`;
  };

  const calculateEarnings = () => {
    if (!isBillable || !hourlyRate) return 0;
    
    const startDateTime = new Date(selectedDate);
    const [startHours, startMinutes] = startTime.split(':').map(Number);
    startDateTime.setHours(startHours, startMinutes, 0, 0);
    
    const endDateTime = new Date(selectedDate);
    const [endHours, endMinutes] = endTime.split(':').map(Number);
    endDateTime.setHours(endHours, endMinutes, 0, 0);
    
    if (endDateTime <= startDateTime) {
      endDateTime.setDate(endDateTime.getDate() + 1);
    }
    
    const hours = (endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60 * 60);
    return hours * hourlyRate;
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[400px] sm:w-[540px] flex flex-col">
        <SheetHeader className="flex-shrink-0">
          <SheetTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            {editEntry ? 'Edit Time Entry' : 'Manual Time Entry'}
          </SheetTitle>
          <SheetDescription>
            {editEntry ? 'Update the time entry details' : 'Add a time entry manually with custom start and end times'}
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto space-y-6 py-6">
          {/* Project and Task Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="project">Project *</Label>
              <Select value={selectedProject} onValueChange={setSelectedProject}>
                <SelectTrigger>
                  <SelectValue placeholder="Select project" />
                </SelectTrigger>
                <SelectContent>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="task">Task (Optional)</Label>
              <Select 
                value={selectedTask} 
                onValueChange={setSelectedTask}
                disabled={!selectedProject}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select task" />
                </SelectTrigger>
                <SelectContent>
                  {tasks.map((task) => (
                    <SelectItem key={task.id} value={task.id}>
                      {task.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Date Selection */}
          <div>
            <Label>Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !selectedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => date && setSelectedDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Time Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="start-time">Start Time</Label>
              <Input
                id="start-time"
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="end-time">End Time</Label>
              <Input
                id="end-time"
                type="time"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
              />
            </div>
          </div>

          {/* Duration Display */}
          <div className="p-4 border rounded-lg bg-muted/20">
            <div className="text-sm font-medium text-muted-foreground mb-1">Duration</div>
            <div className="text-lg font-semibold">{calculateDuration()}</div>
            {isBillable && hourlyRate > 0 && (
              <div className="text-sm text-muted-foreground">
                Earnings: ${calculateEarnings().toFixed(2)}
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="What did you work on?"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="resize-none"
              rows={3}
            />
          </div>

          {/* Billing Settings */}
          <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="billable">Billable Time</Label>
                <p className="text-sm text-muted-foreground">
                  Mark this entry as billable to clients
                </p>
              </div>
              <Switch
                id="billable"
                checked={isBillable}
                onCheckedChange={setIsBillable}
              />
            </div>
            
            {isBillable && (
              <div>
                <Label htmlFor="rate">Hourly Rate</Label>
                <div className="flex items-center mt-1">
                  <span className="text-sm mr-2">$</span>
                  <Input
                    id="rate"
                    type="number"
                    value={hourlyRate}
                    onChange={(e) => setHourlyRate(Number(e.target.value))}
                    min="0"
                    step="0.01"
                    placeholder="0.00"
                  />
                  <span className="text-sm ml-2">/hour</span>
                </div>
              </div>
            )}
          </div>
        </div>

        <SheetFooter className="flex-shrink-0 border-t pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={() => saveEntryMutation.mutate()}
            disabled={!selectedProject || saveEntryMutation.isPending}
            className="gap-2"
          >
            <Save className="w-4 h-4" />
            {saveEntryMutation.isPending
              ? (editEntry ? 'Updating...' : 'Saving...')
              : (editEntry ? 'Update Entry' : 'Save Entry')
            }
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};