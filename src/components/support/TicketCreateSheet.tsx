import { useAuth } from '@/components/auth/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetFooter,
    SheetHeader,
    SheetTitle
} from '@/components/ui/sheet';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { File, Paperclip, Save, Upload, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface TicketCreateSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onTicketCreated: () => void;
}

interface Project {
  id: string;
  name: string;
  project_id: string;
}

interface UploadedFile {
  name: string;
  size: number;
  file: File;
}

export const TicketCreateSheet: React.FC<TicketCreateSheetProps> = ({
  isOpen,
  onClose,
  onTicketCreated,
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [selectedProject, setSelectedProject] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Reset form when opening/closing
  useEffect(() => {
    if (isOpen) {
      setTitle('');
      setDescription('');
      setPriority('medium');
      setSelectedProject('');
      setUploadedFiles([]);
    }
  }, [isOpen]);

  // Fetch projects for selection
  const { data: projects = [] } = useQuery({
    queryKey: ['user-projects'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('projects')
        .select('id, name, project_id')
        .order('name');
      
      if (error) throw error;
      return data as Project[];
    },
    enabled: !!user && isOpen,
  });

  // File upload handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    addFiles(files);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
    const files = Array.from(event.dataTransfer.files);
    addFiles(files);
  };

  const addFiles = (files: File[]) => {
    const newFiles = files.map(file => ({
      name: file.name,
      size: file.size,
      file,
    }));
    setUploadedFiles(prev => [...prev, ...newFiles]);
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Upload files to Supabase Storage
  const uploadFiles = async (ticketId: string) => {
    const uploadPromises = uploadedFiles.map(async ({ file, name }) => {
      const fileExt = name.split('.').pop();
      const fileName = `${ticketId}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      
      const { data, error } = await supabase.storage
        .from('ticket-attachments')
        .upload(fileName, file);

      if (error) throw error;

      // Save file metadata to database
      const { data: fileData, error: dbError } = await (supabase as any)
        .from('file_attachments')
        .insert({
          file_name: name,
          file_url: data.path,
          file_size: file.size,
          file_type: file.type,
          uploaded_by: user!.id,
          // We'll link this to the ticket via a junction table or ticket_id field
        })
        .select()
        .single();

      if (dbError) throw dbError;
      return fileData;
    });

    return Promise.all(uploadPromises);
  };

  const createTicketMutation = useMutation({
    mutationFn: async () => {
      if (!title.trim() || !description.trim()) {
        throw new Error('Title and description are required');
      }

      // Create the ticket
      const { data: ticket, error: ticketError } = await (supabase as any)
        .from('support_tickets')
        .insert({
          title: title.trim(),
          description: description.trim(),
          priority,
          project_id: selectedProject || null,
          created_by: user!.id,
          status: 'open',
        })
        .select()
        .single();

      if (ticketError) throw ticketError;

      // Upload files if any
      if (uploadedFiles.length > 0) {
        try {
          await uploadFiles(ticket.id);
        } catch (fileError) {
          console.error('File upload error:', fileError);
          // Don't fail the ticket creation if file upload fails
        }
      }

      return ticket;
    },
    onSuccess: () => {
      toast({
        title: 'Ticket created',
        description: 'Your support ticket has been submitted successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['support-tickets'] });
      onTicketCreated();
      onClose();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating ticket',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[90vw] sm:w-[700px] md:w-[800px] lg:w-[900px] max-w-[1000px] flex flex-col">
        <SheetHeader className="flex-shrink-0">
          <SheetTitle className="flex items-center gap-2">
            <Paperclip className="w-5 h-5" />
            Create Support Ticket
          </SheetTitle>
          <SheetDescription>
            Submit a new support request or feature request with file attachments
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto space-y-6 py-6">
          {/* Title */}
          <div>
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              placeholder="Brief description of the issue"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>

          {/* Priority */}
          <div>
            <Label htmlFor="priority">Priority</Label>
            <Select value={priority} onValueChange={(value: 'low' | 'medium' | 'high') => setPriority(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Low Priority
                  </div>
                </SelectItem>
                <SelectItem value="medium">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    Medium Priority
                  </div>
                </SelectItem>
                <SelectItem value="high">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    High Priority
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Project */}
          <div>
            <Label htmlFor="project">Related Project (Optional)</Label>
            <Select value={selectedProject} onValueChange={setSelectedProject}>
              <SelectTrigger>
                <SelectValue placeholder="Select related project" />
              </SelectTrigger>
              <SelectContent>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name} ({project.project_id})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              placeholder="Provide detailed information about the issue..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="min-h-[120px] resize-none"
            />
          </div>

          {/* File Upload */}
          <div>
            <Label>Attachments</Label>
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                isDragOver 
                  ? 'border-primary bg-primary/5' 
                  : 'border-border hover:border-primary/50'
              }`}
              onDrop={handleDrop}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragOver(true);
              }}
              onDragLeave={() => setIsDragOver(false)}
            >
              <Upload className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground mb-2">
                Drag and drop files here, or click to browse
              </p>
              <Button variant="outline" size="sm" asChild>
                <label>
                  Browse Files
                  <input
                    type="file"
                    multiple
                    className="hidden"
                    onChange={handleFileSelect}
                    accept="image/*,.pdf,.doc,.docx,.txt"
                  />
                </label>
              </Button>
              <p className="text-xs text-muted-foreground mt-2">
                Supported: Images, PDF, DOC, DOCX, TXT (Max 10MB each)
              </p>
            </div>

            {/* Uploaded Files List */}
            {uploadedFiles.length > 0 && (
              <div className="space-y-2 mt-4">
                <Label className="text-sm font-medium">Uploaded Files ({uploadedFiles.length})</Label>
                {uploadedFiles.map((file, index) => (
                  <Card key={index}>
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <File className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">{file.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {formatFileSize(file.size)}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>

        <SheetFooter className="flex-shrink-0 border-t pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={() => createTicketMutation.mutate()}
            disabled={!title.trim() || !description.trim() || createTicketMutation.isPending}
            className="gap-2"
          >
            <Save className="w-4 h-4" />
            {createTicketMutation.isPending ? 'Creating...' : 'Create Ticket'}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};