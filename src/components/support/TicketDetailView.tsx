import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  ArrowLeft,
  Clock, 
  User, 
  Calendar,
  MessageSquare,
  Paperclip,
  Send,
  AlertTriangle,
  CheckCircle,
  Timer,
  Download,
  Eye,
  FileIcon
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { format, parseISO } from 'date-fns';

interface TicketDetailViewProps {
  ticketId: string;
  onBack: () => void;
}

interface TicketResponse {
  id: string;
  content: string;
  created_at: string;
  user_id: string;
  is_internal: boolean;
  profiles: {
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface TicketAttachment {
  id: string;
  file_name: string;
  file_url: string;
  file_size: number;
  file_type: string;
  uploaded_by: string;
  created_at: string;
}

export const TicketDetailView: React.FC<TicketDetailViewProps> = ({
  ticketId,
  onBack,
}) => {
  const [newResponse, setNewResponse] = useState('');
  const [isInternal, setIsInternal] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [newAssignee, setNewAssignee] = useState('');

  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch ticket details
  const { data: ticket, isLoading } = useQuery({
    queryKey: ['ticket-detail', ticketId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('support_tickets')
        .select(`
          *,
          projects(name, project_id),
          creator:profiles!created_by(first_name, last_name, email),
          assignee:profiles!assigned_to(first_name, last_name, email)
        `)
        .eq('id', ticketId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: !!ticketId,
  });

  // Fetch ticket responses/comments
  const { data: responses = [] } = useQuery({
    queryKey: ['ticket-responses', ticketId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('ticket_responses')
        .select(`
          *,
          profiles(first_name, last_name, email)
        `)
        .eq('ticket_id', ticketId)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      return data as TicketResponse[];
    },
    enabled: !!ticketId,
  });

  // Fetch attachments
  const { data: attachments = [] } = useQuery({
    queryKey: ['ticket-attachments', ticketId],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('file_attachments')
        .select('*')
        .eq('ticket_id', ticketId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as TicketAttachment[];
    },
    enabled: !!ticketId,
  });

  // Fetch team members for assignment
  const { data: teamMembers = [] } = useQuery({
    queryKey: ['team-members'],
    queryFn: async () => {
      const { data, error } = await (supabase as any)
        .from('profiles')
        .select('user_id, first_name, last_name, email')
        .order('first_name');
      
      if (error) throw error;
      return data;
    },
  });

  const addResponseMutation = useMutation({
    mutationFn: async () => {
      if (!newResponse.trim()) throw new Error('Response cannot be empty');

      const { data, error } = await (supabase as any)
        .from('ticket_responses')
        .insert({
          ticket_id: ticketId,
          content: newResponse.trim(),
          user_id: user!.id,
          is_internal: isInternal,
        })
        .select(`
          *,
          profiles(first_name, last_name, email)
        `)
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      setNewResponse('');
      queryClient.invalidateQueries({ queryKey: ['ticket-responses', ticketId] });
      toast({
        title: 'Response added',
        description: 'Your response has been posted successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error adding response',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const updateTicketMutation = useMutation({
    mutationFn: async (updates: any) => {
      const { data, error } = await (supabase as any)
        .from('support_tickets')
        .update(updates)
        .eq('id', ticketId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['ticket-detail', ticketId] });
      queryClient.invalidateQueries({ queryKey: ['support-tickets'] });
      toast({
        title: 'Ticket updated',
        description: 'Ticket has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating ticket',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'destructive';
      case 'in_progress': return 'default';
      case 'resolved': return 'secondary';
      case 'closed': return 'outline';
      default: return 'outline';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'medium': return <Timer className="w-4 h-4" />;
      case 'low': return <CheckCircle className="w-4 h-4" />;
      default: return null;
    }
  };

  const downloadFile = async (attachment: TicketAttachment) => {
    try {
      const { data, error } = await supabase.storage
        .from('ticket-attachments')
        .download(attachment.file_url);

      if (error) throw error;

      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = attachment.file_name;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error: any) {
      toast({
        title: 'Download failed',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase() || 'U';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <h3 className="text-lg font-medium mb-2">Ticket not found</h3>
          <p className="text-muted-foreground">The requested ticket could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Tickets
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Ticket Header */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-xl">{ticket.title}</CardTitle>
                    <Badge variant="outline">{ticket.ticket_id}</Badge>
                  </div>
                  <div className="flex items-center gap-4">
                    <Badge variant={getStatusColor(ticket.status)}>
                      {ticket.status.replace('_', ' ')}
                    </Badge>
                    <Badge variant={getPriorityColor(ticket.priority)} className="gap-1">
                      {getPriorityIcon(ticket.priority)}
                      {ticket.priority} priority
                    </Badge>
                    {ticket.projects && (
                      <Badge variant="outline">
                        {ticket.projects.name}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <p className="whitespace-pre-wrap">{ticket.description}</p>
              </div>
              
              {/* Ticket metadata */}
              <div className="flex items-center gap-6 mt-6 pt-4 border-t text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="w-4 h-4" />
                  Created by {ticket.creator?.first_name} {ticket.creator?.last_name}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {format(parseISO(ticket.created_at), 'MMM d, yyyy at h:mm a')}
                </div>
                {ticket.assignee && (
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    Assigned to {ticket.assignee.first_name} {ticket.assignee.last_name}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Attachments */}
          {attachments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Paperclip className="w-5 h-5" />
                  Attachments ({attachments.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <FileIcon className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">{attachment.file_name}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatFileSize(attachment.file_size)}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => downloadFile(attachment)}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Responses */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="w-5 h-5" />
                Responses ({responses.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {responses.map((response) => (
                    <div key={response.id} className="flex gap-3">
                      <Avatar className="w-8 h-8 mt-1">
                        <AvatarFallback className="text-xs">
                          {getInitials(response.profiles?.first_name, response.profiles?.last_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">
                            {response.profiles?.first_name} {response.profiles?.last_name}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {format(parseISO(response.created_at), 'MMM d, h:mm a')}
                          </span>
                          {response.is_internal && (
                            <Badge variant="outline" className="text-xs">Internal</Badge>
                          )}
                        </div>
                        <div className="text-sm whitespace-pre-wrap bg-muted/30 p-3 rounded-lg">
                          {response.content}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              <Separator className="my-4" />

              {/* Add Response */}
              <div className="space-y-3">
                <Textarea
                  placeholder="Add a response..."
                  value={newResponse}
                  onChange={(e) => setNewResponse(e.target.value)}
                  className="min-h-[100px] resize-none"
                />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id="internal"
                      checked={isInternal}
                      onChange={(e) => setIsInternal(e.target.checked)}
                      className="rounded"
                    />
                    <label htmlFor="internal" className="text-sm">
                      Internal note (not visible to client)
                    </label>
                  </div>
                  <Button
                    onClick={() => addResponseMutation.mutate()}
                    disabled={!newResponse.trim() || addResponseMutation.isPending}
                    size="sm"
                    className="gap-2"
                  >
                    <Send className="w-4 h-4" />
                    {addResponseMutation.isPending ? 'Sending...' : 'Send Response'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status & Assignment */}
          <Card>
            <CardHeader>
              <CardTitle>Ticket Management</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <Select
                  value={newStatus || ticket.status}
                  onValueChange={(value) => {
                    setNewStatus(value);
                    updateTicketMutation.mutate({ status: value });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Assigned To</label>
                <Select
                  value={newAssignee || ticket.assigned_to || ''}
                  onValueChange={(value) => {
                    setNewAssignee(value);
                    updateTicketMutation.mutate({ assigned_to: value || null });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Unassigned" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Unassigned</SelectItem>
                    {teamMembers.map((member) => (
                      <SelectItem key={member.user_id} value={member.user_id}>
                        {member.first_name} {member.last_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Ticket created</span>
                  <span className="text-muted-foreground ml-auto">
                    {format(parseISO(ticket.created_at), 'MMM d')}
                  </span>
                </div>
                {ticket.assigned_to && (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Assigned to {ticket.assignee?.first_name}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  <span>{responses.length} responses</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};